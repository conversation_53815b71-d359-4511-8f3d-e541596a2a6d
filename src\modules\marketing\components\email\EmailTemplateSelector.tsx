import { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Mail, Eye, Settings, X } from 'lucide-react';
import {
  Card,
  Typography,
  Button,
  Alert,
  FormItem,
  Modal,
  ScrollArea,
} from '@/shared/components/common';
import { AsyncSelectWithPagination } from '@/shared/components/common/Select';
import { EmailService } from '../../services/email.service';
import { EmailTemplateAdapterService } from '../../services/email-template-adapter.service';
import { EmailTemplateStatus } from '../../types/email.types';
import type { EmailTemplateDto } from '../../types/email.types';
import { TemplateVariableManager } from './TemplateVariableManager';

interface EmailTemplateSelectorProps {
  value?: string;
  onChange: (templateId: string, templateData?: EmailTemplateDto) => void;
  onVariablesChange?: (variables: Record<string, string>) => void;
  className?: string;
  disabled?: boolean;
}

/**
 * Component chọn Email Template với hiển thị thông tin chi tiết và quản lý biến
 */
export function EmailTemplateSelector({
  value,
  onChange,
  onVariablesChange,
  className = '',
  disabled = false,
}: EmailTemplateSelectorProps) {
  const { t } = useTranslation(['marketing', 'common']);
  
  // State
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplateDto | null>(null);
  const [showVariableManager, setShowVariableManager] = useState(false);
  const [showContentPreview, setShowContentPreview] = useState(false);
  const [templateVariables, setTemplateVariables] = useState<Record<string, string>>({});

  // Load email templates for AsyncSelectWithPagination
  const loadEmailTemplates = useCallback(async (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const response = await EmailService.getTemplates({
        search: params.search || '',
        status: EmailTemplateStatus.ACTIVE,
        page: params.page || 1,
        limit: params.limit || 20,
      });

      const items = response.result.items.map(template => ({
        value: template.id,
        label: `${template.name} - ${template.subject}`,
        data: { template }
      }));

      return {
        items,
        totalItems: response.result.meta.totalItems || 0,
        totalPages: response.result.meta.totalPages || 1,
        currentPage: response.result.meta.currentPage || 1,
      };
    } catch (error) {
      console.error('Error loading email templates:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 1,
        currentPage: 1,
      };
    }
  }, []);

  // Load template detail when selected
  const loadTemplateDetail = useCallback(async (templateId: string) => {
    if (!templateId) {
      setSelectedTemplate(null);
      return;
    }

    try {
      const templateDetail = await EmailTemplateAdapterService.getEmailTemplate(templateId);
      setSelectedTemplate(templateDetail);

      // Initialize variables with default values
      const initialVariables: Record<string, string> = {};
      const variables = templateDetail.variables || [];
      variables.forEach(variable => {
        initialVariables[variable.name] = variable.defaultValue || '';
      });
      setTemplateVariables(initialVariables);
      onVariablesChange?.(initialVariables);

    } catch (error) {
      console.error('Error loading template detail:', error);
      setSelectedTemplate(null);
    }
  }, [onVariablesChange]);

  // Handle template selection
  const handleTemplateChange = useCallback((value: string | string[] | number | number[] | undefined) => {
    if (value === undefined) {
      // Handle deselection
      setSelectedTemplate(null);
      setTemplateVariables({});
      onVariablesChange?.({});
      return;
    }

    const templateId = Array.isArray(value) ? value[0]?.toString() : value?.toString();
    if (templateId) {
      onChange(templateId, selectedTemplate || undefined);
      loadTemplateDetail(templateId);
    }
  }, [onChange, selectedTemplate, loadTemplateDetail, onVariablesChange]);

  // Handle variables change
  const handleVariablesChange = useCallback((variables: Record<string, string>) => {
    setTemplateVariables(variables);
    onVariablesChange?.(variables);
  }, [onVariablesChange]);

  // Load template detail when value changes externally
  useEffect(() => {
    if (value && value !== selectedTemplate?.id) {
      loadTemplateDetail(value);
    }
  }, [value, selectedTemplate?.id, loadTemplateDetail]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Template Selection */}
      <Card
        title={t('marketing:email.campaigns.form.template.title', 'Email Template')}
        subtitle={t('marketing:email.campaigns.form.template.description', 'Chọn template email để sử dụng cho chiến dịch')}
        className="border-0 shadow-none"
      >
        <div className="space-y-4">
          <FormItem
            label={t('marketing:email.campaigns.form.template.label', 'Chọn Template')}
            name="templateId"
            required
          >
            <AsyncSelectWithPagination
              value={value}
              onChange={handleTemplateChange}
              loadOptions={loadEmailTemplates}
              placeholder={t('marketing:email.campaigns.form.template.placeholder', 'Tìm kiếm template email...')}
              debounceTime={300}
              itemsPerPage={20}
              autoLoadInitial={true}
              searchOnEnter={true}
              noOptionsMessage={t('marketing:email.campaigns.form.template.noOptions', 'Không tìm thấy template')}
              loadingMessage={t('marketing:email.campaigns.form.template.loading', 'Đang tải templates...')}
              disabled={disabled}
              fullWidth
            />
          </FormItem>

          {/* Template Preview */}
          {selectedTemplate && (
            <div className="mt-4 p-4 bg-muted/20 rounded-lg">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Mail className="h-5 w-5 text-primary" />
                  <Typography variant="h6" weight="medium">
                    {selectedTemplate.name}
                  </Typography>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowContentPreview(true)}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    {t('marketing:email.template.preview', 'Xem nội dung')}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowVariableManager(!showVariableManager)}
                    disabled={(selectedTemplate.variables || []).length === 0}
                  >
                    <Settings className="h-4 w-4 mr-1" />
                    {t('marketing:email.template.variables.manage', 'Quản lý biến')}
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <Typography variant="caption" color="muted" className="uppercase tracking-wide">
                    {t('marketing:email.template.subject', 'Tiêu đề')}
                  </Typography>
                  <Typography variant="body2" className="mt-1">
                    {selectedTemplate.subject}
                  </Typography>
                </div>

                <div>
                  <Typography variant="caption" color="muted" className="uppercase tracking-wide">
                    {t('marketing:email.template.content', 'Nội dung')}
                  </Typography>
                  <div className="mt-1 p-3 bg-background rounded max-h-32 overflow-y-auto">
                    <Typography variant="body2" color="muted">
                      {selectedTemplate.htmlContent.replace(/<[^>]*>/g, '').substring(0, 150)}...
                    </Typography>
                  </div>
                </div>

                {(selectedTemplate.variables || []).length > 0 && (
                  <div>
                    <Typography variant="caption" color="muted" className="uppercase tracking-wide">
                      {t('marketing:email.template.variables.title', 'Biến trong template')} ({(selectedTemplate.variables || []).length})
                    </Typography>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {(selectedTemplate.variables || []).map((variable) => (
                        <span
                          key={variable.name}
                          className="inline-flex items-center px-2 py-1 rounded-md text-xs bg-primary/10 text-primary"
                        >
                          {variable.name}
                          {variable.required && <span className="ml-1 text-red-500">*</span>}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* No Template Selected */}
          {!selectedTemplate && value && (
            <Alert
              type="info"
              message={t('marketing:email.template.selectPrompt', 'Chọn template để xem thông tin chi tiết')}
            />
          )}
        </div>
      </Card>

      {/* Variable Manager */}
      {selectedTemplate && showVariableManager && (selectedTemplate.variables || []).length > 0 && (
        <TemplateVariableManager
          template={selectedTemplate}
          variables={templateVariables}
          onChange={handleVariablesChange}
          onClose={() => setShowVariableManager(false)}
        />
      )}

      {/* Content Preview Modal */}
      {selectedTemplate && showContentPreview && (
        <Modal
          isOpen={showContentPreview}
          onClose={() => setShowContentPreview(false)}
          size="lg"
          title={selectedTemplate.name}
          footer={
            <div className="flex justify-end">
              <Button variant="outline" onClick={() => setShowContentPreview(false)}>
                <X className="h-4 w-4 mr-1" />
                {t('common:close', 'Đóng')}
              </Button>
            </div>
          }
        >
          <div className="space-y-4">
            <div className="flex items-center gap-2 mb-4">
              <Mail className="h-5 w-5 text-primary" />
              <Typography variant="h6" weight="medium">
                {selectedTemplate.name}
              </Typography>
            </div>
            <div>
              <Typography variant="caption" color="muted" className="uppercase tracking-wide">
                {t('marketing:email.template.subject', 'Tiêu đề')}
              </Typography>
              <Typography variant="h6" className="mt-1">
                {selectedTemplate.subject}
              </Typography>
            </div>

            <div>
              <Typography variant="caption" color="muted" className="uppercase tracking-wide">
                {t('marketing:email.template.content', 'Nội dung')}
              </Typography>
              <div className="mt-2 border rounded-lg overflow-hidden">
                <ScrollArea
                  height="400px"
                  autoHide={true}
                  direction="vertical"
                  className="w-full"
                >
                  <iframe
                    srcDoc={selectedTemplate.htmlContent}
                    className="w-full min-h-96 border-0"
                    title="Email Template Preview"
                  />
                </ScrollArea>
              </div>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
}

export default EmailTemplateSelector;
