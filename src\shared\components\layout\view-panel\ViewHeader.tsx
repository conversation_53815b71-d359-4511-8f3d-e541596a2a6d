import { useTranslation } from 'react-i18next';
import { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Avatar, Dropdown, Icon, LanguageFlag, ThemeToggle } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';
import { useLanguage } from '@/shared/contexts/language';
import { useLogout } from '@/modules/auth/hooks/useAuthQuery';
import { useAuthCommon } from '@/shared/hooks/useAuthCommon';
import { useRPoint } from '@/shared/contexts/useRPoint';
import { useUserPoints } from '@/modules/profile/hooks/useUser';
import { useSafeCartRedux } from '@/modules/marketplace/hooks/useSafeCartRedux';
import ViewBreadcrumb from './ViewBreadcrumb';
import { useProfile } from '@/modules/profile/hooks/useProfile';

interface ViewHeaderProps {
  title: string;
  actions?: React.ReactNode;
}

const ViewHeader = ({ title, actions }: ViewHeaderProps) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { themeMode, toggleTheme } = useTheme();
  const theme = themeMode === 'custom' ? 'light' : themeMode;
  const { language, setLanguage, availableLanguages } = useLanguage();
  const { mutate: logout } = useLogout();
  const { clearAuth } = useAuthCommon();
  // Kiểm tra xem có đang ở trong module marketplace không
  const isMarketplace = location.pathname.startsWith('/marketplace');

  // Lấy số RPoint của người dùng từ context (fallback)
  const { userRPoints: contextRPoints } = useRPoint();

  // Lấy số RPoint của người dùng từ API
  const { data: apiRPoints } = useUserPoints();

  const { data: profile } = useProfile();

  // Log để debug
  console.log('API RPoints:', apiRPoints);
  console.log('Context RPoints:', contextRPoints);

  // Sử dụng điểm từ API nếu có, nếu không thì dùng từ context
  // Đảm bảo userRPoints luôn là một số
  const userRPoints =
    typeof apiRPoints === 'number' && !isNaN(apiRPoints)
      ? apiRPoints
      : typeof contextRPoints === 'number' && !isNaN(contextRPoints)
        ? contextRPoints
        : 0;

  // Lấy số lượng sản phẩm trong giỏ hàng (chỉ khi ở trong module marketplace)
  // Sử dụng useSafeCartRedux để lấy thông tin giỏ hàng từ Redux
  const { totalItems: cartItemCount } = useSafeCartRedux();
  const totalItems = isMarketplace ? cartItemCount : 0;

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Handle language selection dropdown
      const langDropdown = document.getElementById('language-selection-dropdown');
      const langTrigger = document.getElementById('language-trigger');

      if (
        langDropdown &&
        !langDropdown.contains(event.target as Node) &&
        langTrigger &&
        !langTrigger.contains(event.target as Node)
      ) {
        langDropdown.classList.add('hidden');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Sử dụng component LanguageFlag thay vì hàm renderLanguageFlag

  // No separate language dropdown items needed anymore

  // Create profile dropdown items
  const profileItems = [
    {
      id: 'profile',
      label: t('common.profile'),
      onClick: () => navigate('/profile'),
      icon: <Icon name="user" size="sm" />,
    },
    {
      id: 'language-section',
      label: (
        <div className="flex items-center justify-between w-full px-2 py-2">
          <div className="flex items-center">
            <Icon name="language" size="sm" className="mr-2" />
            <span className="mr-3">{t('common.language')}</span>
          </div>

          {/* Current language flag */}
          <LanguageFlag code={language as 'vi' | 'en' | 'zh'} isSelected={true} />
        </div>
      ),
      subItems: availableLanguages.map(lang => ({
        id: `lang-${lang.code}`,
        label: (
          <div className="flex items-center">
            <LanguageFlag code={lang.code as 'vi' | 'en' | 'zh'} />
            <span className="ml-2">{lang.name}</span>
            {language === lang.code && (
              <div className="ml-2 text-primary">
                <Icon name="check" size="sm" fill />
              </div>
            )}
          </div>
        ),
        onClick: () => setLanguage(lang.code as 'vi' | 'en' | 'zh'),
      })),
    },
    {
      id: 'theme-divider',
      divider: true,
    },
    {
      id: 'theme-section',
      label: (
        <div className="flex items-center justify-between w-full px-2 py-2">
          <div className="flex items-center">
            <Icon name={theme === 'light' ? 'sun' : 'moon'} size="sm" className="mr-2" />
            <span className="mr-3">{t('common.theme')}</span>
          </div>

          <ThemeToggle
            theme={theme}
            onToggle={toggleTheme}
            lightText={t('common.light')}
            darkText={t('common.dark')}
          />
        </div>
      ),
      onClick: () => {},
    },
    {
      id: 'settings-divider',
      divider: true,
    },
    {
      id: 'settings',
      label: t('common.settings'),
      onClick: () => console.log('Settings clicked'),
      icon: <Icon name="settings" size="sm" />,
    },
    {
      id: 'logout-divider',
      divider: true,
    },
    {
      id: 'logout',
      label: t('common.logout'),
      onClick: () => {
        logout(undefined, {
          onSuccess: () => {
            // Xóa token khỏi Redux store thông qua hook useAuth
            clearAuth();
            // Chuyển hướng đến trang đăng nhập
            navigate('/auth');
          },
          onError: (error: Error) => {
            console.error('Logout failed:', error);
            // Vẫn xóa token và chuyển hướng đến trang đăng nhập ngay cả khi có lỗi
            clearAuth();
            navigate('/auth');
          },
        });
      },
      icon: <Icon name="logout" size="sm" />,
    },
  ];

  return (
    <div className="flex items-center justify-between p-3 px-4 w-full max-w-full overflow-hidden">
      <div className="flex items-center overflow-hidden">
        <ViewBreadcrumb title={title} />
        {actions && <div className="ml-4 overflow-hidden">{actions}</div>}
      </div>

      <div className="flex items-center space-x-2 sm:space-x-4 relative z-40">
        {/* Hiển thị RPoint - ẩn trên mobile */}
        <div className="hidden sm:flex items-center px-1 py-1">
          <span className="text-sm font-medium mr-1">
            {userRPoints !== undefined && userRPoints !== null && !Number.isNaN(userRPoints)
              ? new Intl.NumberFormat('vi-VN').format(userRPoints)
              : '0'}
          </span>
          <Icon name="rpoint" size="sm" className="text-red-600 mr-1" />
        </div>
        {/* Icon giỏ hàng - ẩn trên mobile */}
        <div
          className="hidden sm:block relative cursor-pointer"
          onClick={e => {
            e.preventDefault();
            // Sử dụng replace: true để thay thế URL hiện tại thay vì thêm vào history stack
            navigate('/marketplace/cart', { replace: true });
          }}
        >
          <Icon
            name="shopping-cart"
            size="md"
            className="text-gray-700 dark:text-gray-300 hover:text-primary"
          />
          {totalItems > 0 && (
            <span className="absolute -top-1 -right-1 bg-red-600 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
              {totalItems}
            </span>
          )}
        </div>

        {/* User dropdown */}
        <Dropdown
          trigger={
            <div className="cursor-pointer flex-shrink-0 min-w-[40px]">
              <Avatar
                src={profile?.avatarUrl || '/assets/images/avatar-user.jpg'}
                alt="User"
                size="md"
                status="online"
                className="flex-shrink-0 min-w-[40px] min-h-[40px] w-10 h-10"
              />
            </div>
          }
          items={profileItems}
          placement="bottom-right"
          width="w-56"
        />
      </div>
    </div>
  );
};

export default ViewHeader;
