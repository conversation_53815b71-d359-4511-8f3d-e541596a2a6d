import { ApiResponseDto as ApiResponse } from '@/shared/dto/response/api-response.dto';
import {
  GetAgentsQueryDto,
  CreateAgentDto,
  UpdateAgentDto,
  AgentStatisticsQueryDto,
  UpdateAgentVectorStoreDto,
  AgentListResponse,
  AgentDetailDto,
  AgentStatisticsResponseDto,
  AgentListItemDto,
  TypeProviderEnum,
  CreateAgentResponseDto,
  UpdateAgentResponseDto,
  TypeAgentListItemDto,
  TypeAgentDetailDto,
  TypeAgentListResponse,
  GetTypeAgentsQueryDto,
} from '../types';

/**
 * Mock data cho AI Agents
 */

// Mock data cho Type Agents
const mockTypeAgents: TypeAgentListItemDto[] = [
  {
    id: 1,
    name: 'Marketing Agent',
    description: 'Specialized in marketing campaigns and content creation',
    config: {
      hasProfile: true,
      hasOutput: true,
      hasConversion: false,
      hasResources: true,
      hasStrategy: true,
      hasMultiAgent: false,
    },
    countTool: 5,
  },
  {
    id: 2,
    name: 'Content Agent',
    description: 'Focused on content creation and writing',
    config: {
      hasProfile: true,
      hasOutput: true,
      hasConversion: true,
      hasResources: true,
      hasStrategy: false,
      hasMultiAgent: false,
    },
    countTool: 3,
  },
  {
    id: 3,
    name: 'Analytics Agent',
    description: 'Data analysis and reporting specialist',
    config: {
      hasProfile: false,
      hasOutput: true,
      hasConversion: true,
      hasResources: true,
      hasStrategy: false,
      hasMultiAgent: false,
    },
    countTool: 7,
  },
  {
    id: 4,
    name: 'Support Agent',
    description: 'Customer support and service assistant',
    config: {
      hasProfile: true,
      hasOutput: false,
      hasConversion: false,
      hasResources: true,
      hasStrategy: false,
      hasMultiAgent: false,
    },
    countTool: 4,
  },
  {
    id: 5,
    name: 'Development Agent',
    description: 'Code assistance and development support',
    config: {
      hasProfile: false,
      hasOutput: true,
      hasConversion: false,
      hasResources: true,
      hasStrategy: false,
      hasMultiAgent: true,
    },
    countTool: 8,
  },
];

const mockTypeAgentDetails: Record<number, TypeAgentDetailDto> = {
  1: {
    ...mockTypeAgents[0],
    updatedAt: Date.now() - 86400000 * 10,
    createdAt: Date.now() - 86400000 * 60,
    tools: [
      { id: 1, name: 'Social Media Scheduler', description: 'Schedule posts across platforms' },
      { id: 2, name: 'Content Generator', description: 'Generate marketing content' },
      { id: 3, name: 'Analytics Tracker', description: 'Track campaign performance' },
      { id: 4, name: 'Email Marketing', description: 'Create and send email campaigns' },
      { id: 5, name: 'SEO Optimizer', description: 'Optimize content for search engines' },
    ],
  },
  2: {
    ...mockTypeAgents[1],
    updatedAt: Date.now() - 86400000 * 5,
    createdAt: Date.now() - 86400000 * 45,
    tools: [
      { id: 6, name: 'Text Editor', description: 'Advanced text editing capabilities' },
      { id: 7, name: 'Grammar Checker', description: 'Check and correct grammar' },
      { id: 8, name: 'Style Guide', description: 'Maintain consistent writing style' },
    ],
  },
  3: {
    ...mockTypeAgents[2],
    updatedAt: Date.now() - 86400000 * 3,
    createdAt: Date.now() - 86400000 * 30,
    tools: [
      { id: 9, name: 'Data Visualizer', description: 'Create charts and graphs' },
      { id: 10, name: 'Statistical Analysis', description: 'Perform statistical calculations' },
      { id: 11, name: 'Report Generator', description: 'Generate automated reports' },
      { id: 12, name: 'Data Cleaner', description: 'Clean and prepare data' },
      { id: 13, name: 'Trend Analyzer', description: 'Identify data trends' },
      { id: 14, name: 'Forecasting Tool', description: 'Predict future trends' },
      { id: 15, name: 'Dashboard Creator', description: 'Create interactive dashboards' },
    ],
  },
  4: {
    ...mockTypeAgents[3],
    updatedAt: Date.now() - 86400000 * 7,
    createdAt: Date.now() - 86400000 * 50,
    tools: [
      { id: 16, name: 'Ticket Manager', description: 'Manage support tickets' },
      { id: 17, name: 'Knowledge Base', description: 'Access support documentation' },
      { id: 18, name: 'Live Chat', description: 'Real-time customer chat' },
      { id: 19, name: 'FAQ Generator', description: 'Generate frequently asked questions' },
    ],
  },
  5: {
    ...mockTypeAgents[4],
    updatedAt: Date.now() - 86400000 * 1,
    createdAt: Date.now() - 86400000 * 20,
    tools: [
      { id: 20, name: 'Code Editor', description: 'Advanced code editing' },
      { id: 21, name: 'Debugger', description: 'Debug code issues' },
      { id: 22, name: 'Code Formatter', description: 'Format code according to standards' },
      { id: 23, name: 'Documentation Generator', description: 'Generate code documentation' },
      { id: 24, name: 'Test Generator', description: 'Generate unit tests' },
      { id: 25, name: 'Code Reviewer', description: 'Review code quality' },
      { id: 26, name: 'Dependency Manager', description: 'Manage project dependencies' },
      { id: 27, name: 'Build Tool', description: 'Build and compile projects' },
    ],
  },
};

const mockAgents: AgentListItemDto[] = [
  {
    id: 'agent-001',
    name: 'Marketing Assistant',
    avatar: '/assets/images/avatars/marketing-agent.png',
    typeId: 1,
    typeName: 'Marketing Agent',
    exp: 2500,
    expMax: 5000,
    level: 3,
    badge_url: '/assets/images/badges/marketing-badge.png',
    model_id: 'gpt-4o',
    active: true,
    createdAt: Date.now() - 86400000 * 30, // 30 days ago
    updatedAt: Date.now() - 86400000 * 2,  // 2 days ago
  },
  {
    id: 'agent-002',
    name: 'Content Creator',
    avatar: '/assets/images/avatars/content-agent.png',
    typeId: 2,
    typeName: 'Content Agent',
    exp: 4200,
    expMax: 5000,
    level: 4,
    badge_url: '/assets/images/badges/content-badge.png',
    model_id: 'claude-3.5-sonnet',
    active: true,
    createdAt: Date.now() - 86400000 * 45, // 45 days ago
    updatedAt: Date.now() - 86400000 * 1,  // 1 day ago
  },
  {
    id: 'agent-003',
    name: 'Data Analyst',
    avatar: '/assets/images/avatars/data-agent.png',
    typeId: 3,
    typeName: 'Analytics Agent',
    exp: 1800,
    expMax: 3000,
    level: 2,
    badge_url: '/assets/images/badges/analytics-badge.png',
    model_id: 'gemini-pro',
    active: false,
    createdAt: Date.now() - 86400000 * 15, // 15 days ago
    updatedAt: Date.now() - 86400000 * 5,  // 5 days ago
  },
  {
    id: 'agent-004',
    name: 'Customer Support',
    avatar: '/assets/images/avatars/support-agent.png',
    typeId: 4,
    typeName: 'Support Agent',
    exp: 3600,
    expMax: 4000,
    level: 5,
    badge_url: '/assets/images/badges/support-badge.png',
    model_id: 'deepseek-chat',
    active: true,
    createdAt: Date.now() - 86400000 * 60, // 60 days ago
    updatedAt: Date.now() - 86400000 * 3,  // 3 days ago
  },
  {
    id: 'agent-005',
    name: 'Code Assistant',
    avatar: '/assets/images/avatars/code-agent.png',
    typeId: 5,
    typeName: 'Development Agent',
    exp: 800,
    expMax: 2000,
    level: 1,
    badge_url: '/assets/images/badges/dev-badge.png',
    model_id: 'llama-3.1-70b',
    active: true,
    createdAt: Date.now() - 86400000 * 7,  // 7 days ago
    updatedAt: Date.now() - 86400000 * 1,  // 1 day ago
  },
];

const mockAgentDetails: Record<string, AgentDetailDto> = {
  'agent-001': {
    ...mockAgents[0],
    modelConfig: {
      temperature: 0.7,
      top_p: 0.9,
      max_tokens: 2048,
    },
    provider_type: TypeProviderEnum.OPENAI,
    instruction: 'You are a marketing assistant specialized in creating engaging content and campaigns.',
    profile: {
      name: 'Marketing Assistant',
      avatar: '/assets/images/avatars/marketing-agent.png',
      gender: 'female',
      position: 'Marketing Specialist',
      education: 'MBA in Marketing',
      skills: ['Content Creation', 'Social Media', 'SEO', 'Analytics'],
      personality: ['Creative', 'Analytical', 'Enthusiastic'],
      languages: ['English', 'Vietnamese'],
      country: 'Vietnam',
    },
    vectorStores: {
      id: 'vs-001',
      name: 'Marketing Knowledge Base',
    },
  },
  'agent-002': {
    ...mockAgents[1],
    modelConfig: {
      temperature: 0.8,
      top_p: 0.95,
      max_tokens: 4096,
    },
    provider_type: TypeProviderEnum.ANTHROPIC,
    instruction: 'You are a content creator focused on producing high-quality, engaging content.',
    profile: {
      name: 'Content Creator',
      avatar: '/assets/images/avatars/content-agent.png',
      gender: 'male',
      position: 'Content Strategist',
      education: 'Bachelor in Communications',
      skills: ['Writing', 'Video Production', 'Graphic Design', 'Storytelling'],
      personality: ['Creative', 'Detail-oriented', 'Collaborative'],
      languages: ['English', 'Vietnamese', 'Chinese'],
      country: 'Vietnam',
    },
    vectorStores: {
      id: 'vs-002',
      name: 'Content Library',
    },
  },
  'agent-003': {
    ...mockAgents[2],
    modelConfig: {
      temperature: 0.3,
      top_p: 0.8,
      max_tokens: 1024,
    },
    provider_type: TypeProviderEnum.GOOGLE,
    instruction: 'You are a data analyst specialized in extracting insights from complex datasets.',
    profile: {
      name: 'Data Analyst',
      avatar: '/assets/images/avatars/data-agent.png',
      gender: 'other',
      position: 'Senior Data Analyst',
      education: 'Master in Data Science',
      skills: ['Python', 'SQL', 'Machine Learning', 'Statistics'],
      personality: ['Analytical', 'Methodical', 'Curious'],
      languages: ['English', 'Vietnamese'],
      country: 'Vietnam',
    },
    vectorStores: {
      id: 'vs-003',
      name: 'Analytics Database',
    },
  },
  'agent-004': {
    ...mockAgents[3],
    modelConfig: {
      temperature: 0.6,
      top_p: 0.85,
      max_tokens: 1536,
    },
    provider_type: TypeProviderEnum.DEEPSEEK,
    instruction: 'You are a customer support agent focused on providing helpful and empathetic assistance.',
    profile: {
      name: 'Customer Support',
      avatar: '/assets/images/avatars/support-agent.png',
      gender: 'female',
      position: 'Customer Success Manager',
      education: 'Bachelor in Business Administration',
      skills: ['Communication', 'Problem Solving', 'Empathy', 'Product Knowledge'],
      personality: ['Helpful', 'Patient', 'Professional'],
      languages: ['English', 'Vietnamese', 'Chinese'],
      country: 'Vietnam',
    },
    vectorStores: {
      id: 'vs-004',
      name: 'Support Knowledge Base',
    },
  },
  'agent-005': {
    ...mockAgents[4],
    modelConfig: {
      temperature: 0.2,
      top_p: 0.7,
      max_tokens: 3072,
    },
    provider_type: TypeProviderEnum.META,
    instruction: 'You are a code assistant specialized in helping developers write clean, efficient code.',
    profile: {
      name: 'Code Assistant',
      avatar: '/assets/images/avatars/code-agent.png',
      gender: 'male',
      position: 'Senior Software Engineer',
      education: 'Master in Computer Science',
      skills: ['JavaScript', 'TypeScript', 'React', 'Node.js', 'Python'],
      personality: ['Logical', 'Detail-oriented', 'Innovative'],
      languages: ['English', 'Vietnamese'],
      country: 'Vietnam',
    },
    vectorStores: {
      id: 'vs-005',
      name: 'Code Documentation',
    },
  },
};

/**
 * Mock API functions cho AI Agents
 */

/**
 * Lấy danh sách agents (Mock)
 */
export const getAgentsMock = async (
  params?: GetAgentsQueryDto
): Promise<ApiResponse<AgentListResponse>> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  let filteredAgents = [...mockAgents];
  
  // Apply filters if provided
  if (params?.search) {
    const searchLower = params.search.toLowerCase();
    filteredAgents = filteredAgents.filter(agent => 
      agent.name.toLowerCase().includes(searchLower) ||
      agent.typeName.toLowerCase().includes(searchLower)
    );
  }
  
  if (params?.typeId) {
    filteredAgents = filteredAgents.filter(agent => agent.typeId === params.typeId);
  }
  
  if (params?.active !== undefined) {
    filteredAgents = filteredAgents.filter(agent => agent.active === params.active);
  }
  
  // Apply pagination
  const page = params?.page || 1;
  const limit = params?.limit || 10;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedAgents = filteredAgents.slice(startIndex, endIndex);
  
  return {
    success: true,
    message: 'Agents retrieved successfully',
    result: {
      data: paginatedAgents,
      pagination: {
        page,
        limit,
        total: filteredAgents.length,
        totalPages: Math.ceil(filteredAgents.length / limit),
      },
    },
  };
};

/**
 * Lấy chi tiết agent (Mock)
 */
export const getAgentDetailMock = async (id: string): Promise<ApiResponse<AgentDetailDto>> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));

  const agentDetail = mockAgentDetails[id];

  if (!agentDetail) {
    return {
      success: false,
      message: 'Agent not found',
      result: null as any,
    };
  }

  return {
    success: true,
    message: 'Agent detail retrieved successfully',
    result: agentDetail,
  };
};

/**
 * Tạo agent mới (Mock)
 */
export const createAgentMock = async (data: CreateAgentDto): Promise<ApiResponse<CreateAgentResponseDto>> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));

  const newAgent: AgentListItemDto = {
    id: `agent-${Date.now()}`,
    name: data.name,
    avatar: '/assets/images/avatars/default-agent.png',
    typeId: data.typeId,
    typeName: `Type ${data.typeId}`,
    exp: 0,
    expMax: 1000,
    level: 1,
    badge_url: '/assets/images/badges/default-badge.png',
    model_id: 'gpt-4o',
    active: true,
    createdAt: Date.now(),
    updatedAt: Date.now(),
  };

  // Add to mock data
  mockAgents.push(newAgent);

  return {
    success: true,
    message: 'Agent created successfully',
    result: {
      id: newAgent.id,
      name: newAgent.name,
      typeId: newAgent.typeId,
      active: newAgent.active,
      createdAt: newAgent.createdAt,
      updatedAt: newAgent.updatedAt,
    },
  };
};

/**
 * Cập nhật agent (Mock)
 */
export const updateAgentMock = async (
  id: string,
  data: UpdateAgentDto
): Promise<ApiResponse<UpdateAgentResponseDto>> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 600));

  const agentIndex = mockAgents.findIndex(agent => agent.id === id);

  if (agentIndex === -1) {
    return {
      success: false,
      message: 'Agent not found',
      result: null as any,
    };
  }

  // Update agent
  if (data.name) mockAgents[agentIndex].name = data.name;
  mockAgents[agentIndex].updatedAt = Date.now();

  // Update detail if exists
  if (mockAgentDetails[id]) {
    if (data.name) mockAgentDetails[id].name = data.name;
    if (data.instruction) mockAgentDetails[id].instruction = data.instruction;
    if (data.modelConfig) {
      mockAgentDetails[id].modelConfig = { ...mockAgentDetails[id].modelConfig, ...data.modelConfig };
    }
    if (data.profile) {
      mockAgentDetails[id].profile = { ...mockAgentDetails[id].profile, ...data.profile };
    }
    mockAgentDetails[id].updatedAt = Date.now();
  }

  return {
    success: true,
    message: 'Agent updated successfully',
    result: {
      id: mockAgents[agentIndex].id,
      name: mockAgents[agentIndex].name,
      updatedAt: mockAgents[agentIndex].updatedAt,
    },
  };
};

/**
 * Xóa agent (Mock)
 */
export const deleteAgentMock = async (id: string): Promise<ApiResponse<void>> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 400));

  const agentIndex = mockAgents.findIndex(agent => agent.id === id);

  if (agentIndex === -1) {
    return {
      success: false,
      message: 'Agent not found',
      result: undefined,
    };
  }

  // Remove from mock data
  mockAgents.splice(agentIndex, 1);
  delete mockAgentDetails[id];

  return {
    success: true,
    message: 'Agent deleted successfully',
    result: undefined,
  };
};

/**
 * Bật/tắt agent (Mock)
 */
export const toggleAgentActiveMock = async (id: string): Promise<ApiResponse<{ active: boolean }>> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));

  const agentIndex = mockAgents.findIndex(agent => agent.id === id);

  if (agentIndex === -1) {
    return {
      success: false,
      message: 'Agent not found',
      result: null as any,
    };
  }

  // Toggle active status
  mockAgents[agentIndex].active = !mockAgents[agentIndex].active;
  mockAgents[agentIndex].updatedAt = Date.now();

  // Update detail if exists
  if (mockAgentDetails[id]) {
    mockAgentDetails[id].active = mockAgents[agentIndex].active;
    mockAgentDetails[id].updatedAt = Date.now();
  }

  return {
    success: true,
    message: `Agent ${mockAgents[agentIndex].active ? 'activated' : 'deactivated'} successfully`,
    result: {
      active: mockAgents[agentIndex].active,
    },
  };
};

/**
 * Lấy thống kê agent (Mock)
 */
export const getAgentStatisticsMock = async (
  id: string,
  params?: AgentStatisticsQueryDto
): Promise<ApiResponse<AgentStatisticsResponseDto>> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 400));

  const agent = mockAgents.find(agent => agent.id === id);

  if (!agent) {
    return {
      success: false,
      message: 'Agent not found',
      result: null as any,
    };
  }

  // Generate mock statistics
  const mockStats: AgentStatisticsResponseDto = {
    totalConversations: Math.floor(Math.random() * 1000) + 100,
    totalMessages: Math.floor(Math.random() * 5000) + 500,
    averageResponseTime: Math.floor(Math.random() * 3000) + 500, // milliseconds
    successRate: Math.floor(Math.random() * 20) + 80, // 80-100%
    lastActiveAt: Date.now() - Math.floor(Math.random() * 86400000), // within last day
    dailyStats: Array.from({ length: 7 }, (_, i) => ({
      date: new Date(Date.now() - i * 86400000).toISOString().split('T')[0],
      conversations: Math.floor(Math.random() * 50) + 10,
      messages: Math.floor(Math.random() * 200) + 50,
      avgResponseTime: Math.floor(Math.random() * 2000) + 500,
    })),
  };

  return {
    success: true,
    message: 'Agent statistics retrieved successfully',
    result: mockStats,
  };
};

/**
 * Cập nhật vector store của agent (Mock)
 */
export const updateAgentVectorStoreMock = async (
  id: string,
  data: UpdateAgentVectorStoreDto
): Promise<ApiResponse<void>> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));

  const agentDetail = mockAgentDetails[id];

  if (!agentDetail) {
    return {
      success: false,
      message: 'Agent not found',
      result: undefined,
    };
  }

  // Update vector store
  if (mockAgentDetails[id]) {
    mockAgentDetails[id].vectorStores = {
      id: data.vectorStoreId,
      name: `Vector Store ${data.vectorStoreId}`,
    };
    mockAgentDetails[id].updatedAt = Date.now();
  }

  return {
    success: true,
    message: 'Agent vector store updated successfully',
    result: undefined,
  };
};

/**
 * Lấy danh sách type agents (Mock)
 */
export const getTypeAgentsMock = async (
  params?: GetTypeAgentsQueryDto
): Promise<ApiResponse<TypeAgentListResponse>> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300));

  let filteredTypeAgents = [...mockTypeAgents];

  // Apply filters if provided
  if (params?.search) {
    const searchLower = params.search.toLowerCase();
    filteredTypeAgents = filteredTypeAgents.filter(typeAgent =>
      typeAgent.name.toLowerCase().includes(searchLower) ||
      (typeAgent.description && typeAgent.description.toLowerCase().includes(searchLower))
    );
  }

  // Apply pagination
  const page = params?.page || 1;
  const limit = params?.limit || 10;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedTypeAgents = filteredTypeAgents.slice(startIndex, endIndex);

  return {
    success: true,
    message: 'Type agents retrieved successfully',
    result: {
      items: paginatedTypeAgents,
      meta: {
        totalItems: filteredTypeAgents.length,
        itemCount: paginatedTypeAgents.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(filteredTypeAgents.length / limit),
        currentPage: page,
        hasItems: paginatedTypeAgents.length > 0,
      },
    },
  };
};

/**
 * Lấy chi tiết type agent (Mock)
 */
export const getTypeAgentDetailMock = async (id: number): Promise<ApiResponse<TypeAgentDetailDto>> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 200));

  const typeAgentDetail = mockTypeAgentDetails[id];

  if (!typeAgentDetail) {
    return {
      success: false,
      message: 'Type agent not found',
      result: null as any,
    };
  }

  return {
    success: true,
    message: 'Type agent detail retrieved successfully',
    result: typeAgentDetail,
  };
};

/**
 * Export tất cả mock functions để sử dụng
 */
export const mockAgentAPI = {
  getAgents: getAgentsMock,
  getAgentDetail: getAgentDetailMock,
  createAgent: createAgentMock,
  updateAgent: updateAgentMock,
  deleteAgent: deleteAgentMock,
  toggleAgentActive: toggleAgentActiveMock,
  getAgentStatistics: getAgentStatisticsMock,
  updateAgentVectorStore: updateAgentVectorStoreMock,
  getTypeAgents: getTypeAgentsMock,
  getTypeAgentDetail: getTypeAgentDetailMock,
};
