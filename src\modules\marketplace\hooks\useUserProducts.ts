import { useMutation, useQueryClient } from '@tanstack/react-query';
import { MarketplaceApiService, CreateProductDto, CreateProductResponse, UpdateProductDto, UpdateProductResponse } from '../services/marketplace-api.service';
import { PRODUCT_QUERY_KEYS } from '../constants/product-query-keys';
import { useMarketplaceNotification } from '../components/common/Notification';

/**
 * Hook để tạo sản phẩm mới
 */
export const useCreateProduct = () => {
  const queryClient = useQueryClient();
  const notification = useMarketplaceNotification();

  return useMutation({
    mutationFn: (data: CreateProductDto) => MarketplaceApiService.createProduct(data),
    onSuccess: (newProduct: CreateProductResponse) => {
      // Làm mới danh sách sản phẩm của user (tất cả queries với prefix này)
      queryClient.invalidateQueries({
        queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCTS,
        exact: false
      });

      console.log('✅ [useCreateProduct] Product created successfully:', newProduct);
      console.log('✅ [useCreateProduct] Product name:', newProduct.product?.name);

      // Kiểm tra cấu trúc response trước khi truy cập
      const productName = newProduct.product?.name || 'Unknown Product';
      notification.product.createSuccess(productName);

      return newProduct;
    },
    onError: (error: Error) => {
      notification.product.createError(error.message);
      throw error;
    }
  });
};

/**
 * Hook để cập nhật sản phẩm
 */
export const useUpdateProduct = () => {
  const queryClient = useQueryClient();
  const notification = useMarketplaceNotification();

  return useMutation({
    mutationFn: ({ productId, data }: { productId: number; data: UpdateProductDto }) =>
      MarketplaceApiService.updateProduct(productId, data),
    onSuccess: (updateResponse: UpdateProductResponse) => {
      // Làm mới danh sách sản phẩm của user (tất cả queries với prefix này)
      queryClient.invalidateQueries({
        queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCTS,
        exact: false
      });

      // Làm mới chi tiết sản phẩm nếu có
      queryClient.invalidateQueries({
        queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCT_DETAIL(updateResponse.product.id)
      });

      notification.product.updateSuccess(updateResponse.product.name);

      return updateResponse;
    },
    onError: (error: Error) => {
      notification.product.updateError(error.message);
      throw error;
    }
  });
};

/**
 * Hook để xóa sản phẩm
 */
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();
  const notification = useMarketplaceNotification();

  return useMutation({
    mutationFn: (productId: number) => {
      console.log('🔍 [useDeleteProduct] Deleting product ID:', productId);
      return MarketplaceApiService.deleteProduct(productId);
    },
    onSuccess: () => {
      console.log('✅ [useDeleteProduct] Product deleted successfully, invalidating queries...');

      // Làm mới tất cả queries có prefix USER_PRODUCTS (bao gồm cả có params)
      queryClient.invalidateQueries({
        queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCTS,
        exact: false // Invalidate tất cả queries bắt đầu với key này
      });

      // Cũng invalidate các queries có thể có params
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          return Array.isArray(queryKey) &&
                 queryKey.length >= 3 &&
                 queryKey[0] === 'marketplace' &&
                 queryKey[1] === 'products' &&
                 queryKey[2] === 'user';
        }
      });

      console.log('✅ [useDeleteProduct] Queries invalidated');

      notification.product.deleteSuccess();
    },
    onError: (error: Error) => {
      console.error('❌ [useDeleteProduct] Error deleting product:', error);
      notification.product.deleteError(error.message);
      throw error;
    }
  });
};

/**
 * Hook để gửi sản phẩm để duyệt
 */
export const useSubmitProductForApproval = () => {
  const queryClient = useQueryClient();
  const notification = useMarketplaceNotification();

  return useMutation({
    mutationFn: (productId: number) => MarketplaceApiService.submitProductForApproval(productId),
    onSuccess: (updatedProduct) => {
      // Làm mới danh sách sản phẩm của user
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCTS });

      // Làm mới chi tiết sản phẩm nếu có
      queryClient.invalidateQueries({
        queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCT_DETAIL(updatedProduct.id)
      });

      notification.product.submitForApprovalSuccess(updatedProduct.name);

      return updatedProduct;
    },
    onError: (error: Error) => {
      notification.product.submitForApprovalError(error.message);
      throw error;
    }
  });
};

/**
 * Hook để hủy gửi duyệt sản phẩm
 */
export const useCancelProductSubmission = () => {
  const queryClient = useQueryClient();
  const notification = useMarketplaceNotification();

  return useMutation({
    mutationFn: (productId: number) => MarketplaceApiService.cancelProductSubmission(productId),
    onSuccess: (updatedProduct) => {
      // Làm mới danh sách sản phẩm của user
      queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCTS });

      // Làm mới chi tiết sản phẩm nếu có
      queryClient.invalidateQueries({
        queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCT_DETAIL(updatedProduct.id)
      });

      notification.product.cancelSubmissionSuccess(updatedProduct.name);

      return updatedProduct;
    },
    onError: (error: Error) => {
      notification.product.cancelSubmissionError(error.message);
      throw error;
    }
  });
};

/**
 * Hook để xóa nhiều sản phẩm
 */
export const useBatchDeleteProducts = () => {
  const queryClient = useQueryClient();
  const notification = useMarketplaceNotification();

  return useMutation({
    mutationFn: (productIds: number[]) => {
      console.log('🔍 [useBatchDeleteProducts] Deleting products:', productIds);
      return MarketplaceApiService.batchDeleteProducts(productIds);
    },
    onSuccess: (_, productIds) => {
      console.log('✅ [useBatchDeleteProducts] Products deleted successfully, invalidating queries...');

      // Làm mới tất cả queries có prefix USER_PRODUCTS
      queryClient.invalidateQueries({
        queryKey: PRODUCT_QUERY_KEYS.USER_PRODUCTS,
        exact: false
      });

      // Cũng invalidate các queries có thể có params
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey;
          return Array.isArray(queryKey) &&
                 queryKey.length >= 3 &&
                 queryKey[0] === 'marketplace' &&
                 queryKey[1] === 'products' &&
                 queryKey[2] === 'user';
        }
      });

      console.log('✅ [useBatchDeleteProducts] Queries invalidated');

      notification.product.batchDeleteSuccess(productIds.length);
    },
    onError: (error: Error) => {
      console.error('❌ [useBatchDeleteProducts] Error deleting products:', error);
      notification.product.batchDeleteError(error.message);
      throw error;
    }
  });
};
