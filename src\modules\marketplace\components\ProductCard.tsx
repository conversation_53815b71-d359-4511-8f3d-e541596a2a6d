import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Typography, Icon, ResponsiveImage, Avatar, Chip } from '@/shared/components/common';
import { ProductListItem } from '../types/product.types';
import { useTheme } from '@/shared/contexts';

export interface ProductCardProps {
  /**
   * Dữ liệu sản phẩm
   */
  product: ProductListItem;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Callback khi click vào category
   */
  onCategoryClick?: (categorySlug: string, e: React.MouseEvent) => void;
}

/**
 * Component hiển thị card sản phẩm với thumbnail, tên, giá và thông tin khác
 */
const ProductCard: React.FC<ProductCardProps> = ({ product, className = '', onCategoryClick }) => {
  const navigate = useNavigate();
  useTheme(); // Sử dụng hook theme

  // Xử lý khi click vào category
  const handleCategoryClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (onCategoryClick && product.category) {
      onCategoryClick(product.category, e);
    } else if (product.category) {
      navigate(`/marketplace/category/${product.category}`);
    }
  };

  // Format giá tiền
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'decimal',
      maximumFractionDigits: 0,
    }).format(price);
  };

  // Xử lý khi click vào card
  const handleCardClick = () => {
    navigate(`/marketplace/product/${product.id}`);
  };

  return (
    <Card
      className={`group flex flex-col h-full overflow-hidden p-0 rounded-xl hover:shadow-lg transition-shadow duration-300 cursor-pointer ${className}`}
      noPadding={true}
      allowOverflow={false}
      style={{ display: 'flex', flexDirection: 'column' }}
      onClick={handleCardClick}
    >
      {/* Product thumbnail */}
      <div className="relative w-full">
        <div className="w-full h-48 overflow-hidden rounded-t-xl">
          <ResponsiveImage
            src={product.thumbnail || '/placeholder-image.jpg'}
            alt={product.name}
            className="h-full w-full object-cover transition-transform duration-300 ease-in-out group-hover:scale-110"
            aspectRatio="1/1"
            lazy={true}
            style={{ display: 'block' }}
          />
        </div>

        {/* Đã xóa tất cả các badge và tag hiển thị trên hình ảnh sản phẩm */}
      </div>

      {/* Product content */}
      <div className="p-4 flex-grow flex flex-col mt-0">
        {/* Title - chỉ hiển thị 2 dòng với dấu ba chấm */}
        <Typography
          variant="body1"
          className="text-base font-semibold line-clamp-2 text-left text-black dark:text-white hover:text-primary transition-colors mb-4"
        >
          {product.name}
        </Typography>

        {/* Price section - hiển thị theo yêu cầu mới */}
        <div className="mb-4">
          <div className="flex justify-end items-center">
            {/* Original price - góc trái */}
            <div className="flex items-center">
              <Typography variant="h5" color="danger">
                {formatPrice(product.price || product.discountedPrice || 0)}
              </Typography>
              <Icon name="rpoint" size="sm" className="ml-1 " />
            </div>
            {product.originalPrice ? (
              <div className="flex items-center">
                <Typography
                  variant="body2"
                  color="muted"
                  className="text-base line-through mt-1 ml-1"
                >
                  {formatPrice(product.originalPrice)}
                </Typography>
              </div>
            ) : (
              <div></div> // Div trống để giữ layout khi không có giá gốc
            )}

            {/* Current price - góc phải */}
          </div>
        </div>

        {/* Seller info and sold count */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <Avatar
              src={product.seller.avatar}
              alt={product.seller.name}
              size="sm"
              className="mr-2"
            />
            <Typography
              variant="caption"
              className="text-xs font-medium text-gray-800 dark:text-gray-300"
            >
              {product.seller.name}
            </Typography>
          </div>
          <Typography
            variant="caption"
            className="text-xs font-medium text-gray-600 dark:text-gray-400"
          >
            Đã bán{' '}
            {product.soldCount ? new Intl.NumberFormat('vi-VN').format(product.soldCount) : '0'}
          </Typography>
        </div>

        {/* Bottom row with category tag and shopping cart icon */}
        <div className="flex items-center justify-between mt-auto">
          {/* Category tag */}
          <div onClick={handleCategoryClick} className="z-10">
            <Chip variant="primary" size="sm" className="bg-red-600 text-white hover:bg-red-700">
              {product.category}
            </Chip>
          </div>

          {/* Shopping cart icon */}
          <div className="w-8 h-8 rounded-full bg-red-600 flex items-center justify-center">
            <Icon name="shopping-cart" size="sm" className="text-white" />
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ProductCard;
